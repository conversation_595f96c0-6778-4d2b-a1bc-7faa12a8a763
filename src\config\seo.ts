/**
 * SEO Configuration
 * Central configuration for all SEO-related settings
 */

export const seoConfig = {
  // Base URL (should be set via environment variable in production)
  baseUrl: process.env.VITE_BASE_URL || 'https://www.crash.events',
  
  // Default meta tags
  defaultTitle: 'Crash Events | Professional Artist Management & Booking',
  defaultDescription: 'Professional artist management and booking services for electronic music artists worldwide. Discover exceptional talent for your next event.',
  defaultKeywords: 'artist management, electronic music, booking agency, music events, DJ booking, artist representation',
  
  // Social media
  twitterHandle: '@crash_events',
  facebookAppId: '', // Add when available
  
  // Images
  defaultOgImage: '/images/og-image.jpg',
  logoUrl: '/logo/logo-dark.svg',
  faviconUrl: '/favicon.ico',
  
  // Organization info for structured data
  organization: {
    name: 'Crash Events',
    legalName: 'Crash Events BVBA',
    url: 'https://www.crash.events',
    logo: '/logo/logo-dark.svg',
    foundingDate: '2014',
    founders: [
      '<PERSON>',
      '<PERSON>',
    ],
    address: {
      streetAddress: '', // Add when available
      addressLocality: '', // Add when available
      addressRegion: '', // Add when available
      postalCode: '', // Add when available
      addressCountry: 'BE',
    },
    contactPoint: [
      {
        contactType: 'customer service',
        availableLanguage: ['English', 'Dutch', 'French'],
        email: '<EMAIL>', // Update with actual email
      },
    ],
    sameAs: [
      // Add social media URLs when available
      // 'https://www.facebook.com/crashevents',
      // 'https://www.instagram.com/crashevents',
      // 'https://twitter.com/crash_events',
      // 'https://www.linkedin.com/company/crash-events',
    ],
  },
  
  // Supported languages
  languages: [
    { code: 'en', name: 'English', locale: 'en_US' },
    { code: 'nl', name: 'Dutch', locale: 'nl_NL' },
    { code: 'fr', name: 'French', locale: 'fr_FR' },
  ],
  
  // Default language
  defaultLanguage: 'en',
  
  // Page-specific SEO settings
  pages: {
    home: {
      priority: 1.0,
      changefreq: 'weekly',
    },
    artists: {
      priority: 0.9,
      changefreq: 'daily',
    },
    agency: {
      priority: 0.8,
      changefreq: 'monthly',
    },
    about: {
      priority: 0.7,
      changefreq: 'monthly',
    },
    contact: {
      priority: 0.6,
      changefreq: 'monthly',
    },
    artist: {
      priority: 0.6,
      changefreq: 'weekly',
    },
  },
  
  // Robots.txt settings
  robots: {
    allow: [
      '/',
      '/en/',
      '/nl/',
      '/fr/',
    ],
    disallow: [
      '/admin/',
      '/_next/',
      '/api/',
      '/private/',
    ],
    crawlDelay: 1,
  },
  
  // Analytics and tracking
  analytics: {
    googleAnalyticsId: '', // Add when available
    googleTagManagerId: '', // Add when available
    facebookPixelId: '', // Add when available
  },
  
  // Performance and technical SEO
  technical: {
    // Enable/disable features
    enableStructuredData: true,
    enableBreadcrumbs: true,
    enableHreflang: true,
    enableCanonical: true,
    enableOpenGraph: true,
    enableTwitterCards: true,
    
    // Cache settings
    cacheMaxAge: 3600, // 1 hour
    
    // Image optimization
    imageFormats: ['webp', 'jpg', 'png'],
    imageSizes: [320, 640, 768, 1024, 1280, 1920],
  },
  
  // Content settings
  content: {
    // Maximum lengths for meta tags
    titleMaxLength: 60,
    descriptionMaxLength: 160,
    keywordsMaxLength: 255,
    
    // Default content
    defaultAuthor: 'Crash Events Team',
    copyrightNotice: '© 2024 Crash Events. All rights reserved.',
  },
  
  // Local SEO (for future use)
  local: {
    businessType: 'Entertainment Agency',
    serviceArea: [
      'Belgium',
      'Netherlands',
      'France',
      'Germany',
      'United Kingdom',
    ],
    priceRange: '$$',
  },
};

/**
 * Get SEO config for specific environment
 */
export const getSEOConfig = (env: 'development' | 'production' = 'production') => {
  const config = { ...seoConfig };
  
  if (env === 'development') {
    config.baseUrl = 'http://localhost:8080';
    config.technical.enableStructuredData = false; // Disable in dev to reduce noise
  }
  
  return config;
};

/**
 * Validate SEO configuration
 */
export const validateSEOConfig = (config: typeof seoConfig): string[] => {
  const errors: string[] = [];
  
  if (!config.baseUrl) {
    errors.push('Base URL is required');
  }
  
  if (!config.defaultTitle) {
    errors.push('Default title is required');
  }
  
  if (!config.defaultDescription) {
    errors.push('Default description is required');
  }
  
  if (config.defaultTitle.length > config.content.titleMaxLength) {
    errors.push(`Default title exceeds maximum length of ${config.content.titleMaxLength} characters`);
  }
  
  if (config.defaultDescription.length > config.content.descriptionMaxLength) {
    errors.push(`Default description exceeds maximum length of ${config.content.descriptionMaxLength} characters`);
  }
  
  return errors;
};

export default seoConfig;
