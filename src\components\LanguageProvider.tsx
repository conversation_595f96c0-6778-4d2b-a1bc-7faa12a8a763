import React, { Suspense, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';

interface LanguageProviderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Language Provider component that handles language initialization and loading states
 */
export const LanguageProvider: React.FC<LanguageProviderProps> = ({ 
  children, 
  fallback = <div className="flex items-center justify-center min-h-screen">Loading...</div> 
}) => {
  const { currentLanguage } = useLanguage();

  useEffect(() => {
    // Set document language attribute
    document.documentElement.lang = currentLanguage;
    
    // Set document direction (for future RTL support)
    document.documentElement.dir = 'ltr'; // Add RTL logic here if needed
  }, [currentLanguage]);

  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  );
};

export default LanguageProvider;
