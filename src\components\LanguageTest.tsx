import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';

/**
 * Test component to verify multilingual functionality
 * Remove this component after testing
 */
export const LanguageTest: React.FC = () => {
  const { t } = useTranslation(['common', 'pages']);
  const { currentLanguage, changeLanguage, languages } = useLanguage();

  return (
    <div className="p-6 border rounded-lg bg-background">
      <h2 className="text-xl font-bold mb-4">Language Test Component</h2>
      
      <div className="space-y-4">
        <div>
          <strong>Current Language:</strong> {currentLanguage}
        </div>
        
        <div>
          <strong>Navigation Translations:</strong>
          <ul className="list-disc list-inside ml-4">
            <li>Artists: {t('navigation.artists')}</li>
            <li>Agency: {t('navigation.agency')}</li>
            <li>About: {t('navigation.about')}</li>
            <li>Contact: {t('navigation.contact')}</li>
          </ul>
        </div>
        
        <div>
          <strong>Common Translations:</strong>
          <ul className="list-disc list-inside ml-4">
            <li>Loading: {t('common.loading')}</li>
            <li>Search: {t('common.search')}</li>
            <li>Save: {t('common.save')}</li>
          </ul>
        </div>
        
        <div>
          <strong>Page Translations:</strong>
          <ul className="list-disc list-inside ml-4">
            <li>About Title: {t('pages:about.title')}</li>
            <li>Home Title: {t('pages:home.title')}</li>
          </ul>
        </div>
        
        <div>
          <strong>Language Switcher:</strong>
          <div className="flex gap-2 mt-2">
            {languages.map((lang) => (
              <Button
                key={lang.code}
                variant={currentLanguage === lang.code ? "default" : "outline"}
                size="sm"
                onClick={() => changeLanguage(lang.code)}
              >
                {lang.code.toUpperCase()} - {lang.nativeName}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LanguageTest;
