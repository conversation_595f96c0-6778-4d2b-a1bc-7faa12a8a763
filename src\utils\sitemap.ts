import { languages } from '@/lib/i18n';

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
  alternates?: Array<{
    hreflang: string;
    href: string;
  }>;
}

/**
 * Generate sitemap URLs for all pages and languages
 */
export const generateSitemapUrls = (baseUrl: string): SitemapUrl[] => {
  const currentDate = new Date().toISOString().split('T')[0];
  
  // Define all pages with their properties
  const pages = [
    {
      path: '/',
      changefreq: 'weekly' as const,
      priority: 1.0,
    },
    {
      path: '/agency',
      changefreq: 'monthly' as const,
      priority: 0.8,
    },
    {
      path: '/about',
      changefreq: 'monthly' as const,
      priority: 0.7,
    },
    {
      path: '/contact',
      changefreq: 'monthly' as const,
      priority: 0.6,
    },
  ];

  const sitemapUrls: SitemapUrl[] = [];

  // Generate URLs for each page and language combination
  pages.forEach(page => {
    languages.forEach(lang => {
      const isDefault = lang.code === 'en';
      const langPrefix = isDefault ? '' : `/${lang.code}`;
      const fullPath = page.path === '/' ? langPrefix || '/' : `${langPrefix}${page.path}`;
      
      // Generate alternate language links
      const alternates = languages.map(altLang => {
        const altIsDefault = altLang.code === 'en';
        const altLangPrefix = altIsDefault ? '' : `/${altLang.code}`;
        const altFullPath = page.path === '/' ? altLangPrefix || '/' : `${altLangPrefix}${page.path}`;
        
        return {
          hreflang: altLang.code,
          href: `${baseUrl}${altFullPath}`,
        };
      });

      // Add x-default for international targeting (use English version)
      alternates.push({
        hreflang: 'x-default',
        href: `${baseUrl}${page.path}`,
      });

      sitemapUrls.push({
        loc: `${baseUrl}${fullPath}`,
        lastmod: currentDate,
        changefreq: page.changefreq,
        priority: page.priority,
        alternates,
      });
    });
  });

  return sitemapUrls;
};

/**
 * Generate XML sitemap content
 */
export const generateSitemapXML = (baseUrl: string): string => {
  const urls = generateSitemapUrls(baseUrl);
  
  const urlElements = urls.map(url => {
    const alternateLinks = url.alternates?.map(alt => 
      `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />`
    ).join('\n') || '';

    return `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
${alternateLinks}
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${urlElements}
</urlset>`;
};

/**
 * Generate robots.txt content
 */
export const generateRobotsTxt = (baseUrl: string): string => {
  return `User-agent: *
Allow: /

# Language-specific pages
Allow: /en/
Allow: /nl/
Allow: /fr/

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1

# Block development/admin paths (if any)
Disallow: /admin/
Disallow: /_next/
Disallow: /api/

# Allow social media crawlers
User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /`;
};

/**
 * Generate sitemap index for multiple sitemaps (if needed in the future)
 */
export const generateSitemapIndex = (baseUrl: string, sitemaps: string[]): string => {
  const currentDate = new Date().toISOString();
  
  const sitemapElements = sitemaps.map(sitemap => `  <sitemap>
    <loc>${baseUrl}/${sitemap}</loc>
    <lastmod>${currentDate}</lastmod>
  </sitemap>`).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapElements}
</sitemapindex>`;
};

/**
 * Utility to add artist pages to sitemap (for future use)
 */
export const addArtistPagesToSitemap = (
  baseUrls: SitemapUrl[], 
  artists: Array<{ id: string; name: string; lastModified?: string }>,
  baseUrl: string
): SitemapUrl[] => {
  const artistUrls: SitemapUrl[] = [];
  
  artists.forEach(artist => {
    languages.forEach(lang => {
      const isDefault = lang.code === 'en';
      const langPrefix = isDefault ? '' : `/${lang.code}`;
      const artistPath = `/artist/${artist.id}`;
      const fullPath = `${langPrefix}${artistPath}`;
      
      // Generate alternate language links for artist pages
      const alternates = languages.map(altLang => {
        const altIsDefault = altLang.code === 'en';
        const altLangPrefix = altIsDefault ? '' : `/${altLang.code}`;
        const altFullPath = `${altLangPrefix}${artistPath}`;
        
        return {
          hreflang: altLang.code,
          href: `${baseUrl}${altFullPath}`,
        };
      });

      // Add x-default
      alternates.push({
        hreflang: 'x-default',
        href: `${baseUrl}${artistPath}`,
      });

      artistUrls.push({
        loc: `${baseUrl}${fullPath}`,
        lastmod: artist.lastModified || new Date().toISOString().split('T')[0],
        changefreq: 'weekly',
        priority: 0.6,
        alternates,
      });
    });
  });
  
  return [...baseUrls, ...artistUrls];
};
