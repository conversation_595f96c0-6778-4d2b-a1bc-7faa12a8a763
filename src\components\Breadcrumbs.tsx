import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  name: string;
  url: string;
  isActive?: boolean;
}

interface BreadcrumbsProps {
  /**
   * Custom breadcrumb items (optional)
   */
  items?: BreadcrumbItem[];
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Show home icon instead of text
   */
  showHomeIcon?: boolean;
  
  /**
   * Maximum number of items to show before truncating
   */
  maxItems?: number;
}

/**
 * Breadcrumbs component for navigation and SEO
 */
export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  items,
  className,
  showHomeIcon = false,
  maxItems = 5,
}) => {
  const { t } = useTranslation('seo');
  const location = useLocation();

  // Generate breadcrumbs from current path if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { name: t('breadcrumbs.home'), url: '/', isActive: location.pathname === '/' },
    ];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isActive = index === pathSegments.length - 1;
      
      // Map path segments to breadcrumb names
      let breadcrumbName = segment;
      
      // Try to get translated name
      const translatedName = t(`breadcrumbs.${segment}`, { defaultValue: '' });
      if (translatedName) {
        breadcrumbName = translatedName;
      } else {
        // Fallback: capitalize and replace hyphens with spaces
        breadcrumbName = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }
      
      breadcrumbs.push({
        name: breadcrumbName,
        url: currentPath,
        isActive,
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = items || generateBreadcrumbs();
  
  // Truncate breadcrumbs if they exceed maxItems
  const displayItems = breadcrumbItems.length > maxItems
    ? [
        breadcrumbItems[0], // Always show home
        { name: '...', url: '', isActive: false },
        ...breadcrumbItems.slice(-2), // Show last 2 items
      ]
    : breadcrumbItems;

  if (breadcrumbItems.length <= 1) {
    return null; // Don't show breadcrumbs if only home
  }

  return (
    <nav
      aria-label="Breadcrumb"
      className={cn(
        "flex items-center space-x-1 text-sm text-muted-foreground",
        className
      )}
    >
      <ol className="flex items-center space-x-1">
        {displayItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight
                size={14}
                className="mx-2 text-muted-foreground/60"
                aria-hidden="true"
              />
            )}
            
            {item.name === '...' ? (
              <span className="text-muted-foreground/60">...</span>
            ) : item.isActive ? (
              <span
                className="font-medium text-foreground"
                aria-current="page"
              >
                {index === 0 && showHomeIcon ? (
                  <Home size={14} aria-label={item.name} />
                ) : (
                  item.name
                )}
              </span>
            ) : (
              <Link
                to={item.url}
                className="hover:text-foreground transition-colors"
                aria-label={`Go to ${item.name}`}
              >
                {index === 0 && showHomeIcon ? (
                  <Home size={14} aria-label={item.name} />
                ) : (
                  item.name
                )}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

/**
 * Breadcrumb structured data component (invisible, for SEO)
 */
export const BreadcrumbStructuredData: React.FC<{
  items?: BreadcrumbItem[];
}> = ({ items }) => {
  const location = useLocation();
  const { t } = useTranslation('seo');

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { name: t('breadcrumbs.home'), url: '/' },
    ];

    let currentPath = '';
    pathSegments.forEach(segment => {
      currentPath += `/${segment}`;
      const translatedName = t(`breadcrumbs.${segment}`, { defaultValue: segment });
      breadcrumbs.push({
        name: translatedName,
        url: currentPath,
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = items || generateBreadcrumbs();
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbItems.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${baseUrl}${item.url}`,
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
};

export default Breadcrumbs;
