import React from 'react';
import { Trans, useTranslation } from 'react-i18next';

interface TranslationProps {
  /**
   * Translation key
   */
  i18nKey: string;
  
  /**
   * Namespace (optional, defaults to 'common')
   */
  ns?: string;
  
  /**
   * Values for interpolation
   */
  values?: Record<string, any>;
  
  /**
   * Components for Trans component
   */
  components?: Record<string, React.ReactElement>;
  
  /**
   * Default value if translation is missing
   */
  defaults?: string;
  
  /**
   * Additional props to pass to the Trans component
   */
  transProps?: any;
}

/**
 * Translation component for complex translations with HTML support
 * Uses react-i18next's Trans component under the hood
 */
export const Translation: React.FC<TranslationProps> = ({
  i18nKey,
  ns = 'common',
  values,
  components,
  defaults,
  transProps,
}) => {
  return (
    <Trans
      i18nKey={i18nKey}
      ns={ns}
      values={values}
      components={components}
      defaults={defaults}
      {...transProps}
    />
  );
};

/**
 * Simple text translation component
 */
interface TProps {
  /**
   * Translation key
   */
  k: string;
  
  /**
   * Namespace (optional)
   */
  ns?: string;
  
  /**
   * Values for interpolation
   */
  values?: Record<string, any>;
  
  /**
   * Default value
   */
  defaults?: string;
}

export const T: React.FC<TProps> = ({ k, ns, values, defaults }) => {
  const { t } = useTranslation(ns);
  return <>{t(k, { ...values, defaultValue: defaults })}</>;
};

/**
 * Hook for getting translated strings
 */
export const useT = (namespace?: string) => {
  const { t } = useTranslation(namespace);
  return t;
};

export default Translation;
