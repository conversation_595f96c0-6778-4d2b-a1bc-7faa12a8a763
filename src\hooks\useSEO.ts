import { useTranslation } from 'react-i18next';
import { useLanguage } from './useLanguage';
import { useLocation } from 'react-router-dom';

/**
 * Hook for SEO utilities and page-specific SEO data
 */
export const useSEO = () => {
  const { t } = useTranslation('seo');
  const { currentLanguage } = useLanguage();
  const location = useLocation();

  /**
   * Get SEO data for a specific page
   */
  const getPageSEO = (pageKey: string, variables?: Record<string, string>) => {
    const interpolateString = (str: string, vars: Record<string, string> = {}) => {
      return str.replace(/\{\{(\w+)\}\}/g, (match, key) => vars[key] || match);
    };

    const title = t(`pages.${pageKey}.title`, { defaultValue: '' });
    const description = t(`pages.${pageKey}.description`, { defaultValue: '' });
    const keywords = t(`pages.${pageKey}.keywords`, { defaultValue: '' });

    return {
      title: variables ? interpolateString(title, variables) : title,
      description: variables ? interpolateString(description, variables) : description,
      keywords: variables ? interpolateString(keywords, variables) : keywords,
    };
  };

  /**
   * Generate structured data for the website
   */
  const getWebsiteStructuredData = () => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: t('site.name'),
      description: t('site.description'),
      url: baseUrl,
      logo: `${baseUrl}/logo/logo-dark.svg`,
      sameAs: [
        // Add social media URLs here when available
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        contactType: 'customer service',
        availableLanguage: ['English', 'Dutch', 'French'],
      },
    };
  };

  /**
   * Generate structured data for an artist page
   */
  const getArtistStructuredData = (artistData: {
    name: string;
    description?: string;
    image?: string;
    genre?: string[];
    url?: string;
  }) => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    
    return {
      '@context': 'https://schema.org',
      '@type': 'Person',
      '@id': artistData.url || `${baseUrl}${location.pathname}`,
      name: artistData.name,
      description: artistData.description,
      image: artistData.image,
      jobTitle: 'Electronic Music Artist',
      worksFor: {
        '@type': 'Organization',
        name: t('site.name'),
      },
      genre: artistData.genre,
      url: artistData.url || `${baseUrl}${location.pathname}`,
    };
  };

  /**
   * Generate breadcrumb structured data
   */
  const getBreadcrumbStructuredData = (breadcrumbs: Array<{ name: string; url: string }>) => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: crumb.name,
        item: `${baseUrl}${crumb.url}`,
      })),
    };
  };

  /**
   * Generate FAQ structured data
   */
  const getFAQStructuredData = (faqs: Array<{ question: string; answer: string }>) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map(faq => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer,
        },
      })),
    };
  };

  /**
   * Get current page breadcrumbs
   */
  const getCurrentBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [
      { name: t('breadcrumbs.home'), url: '/' },
    ];

    let currentPath = '';
    pathSegments.forEach(segment => {
      currentPath += `/${segment}`;
      
      // Map path segments to breadcrumb names
      const breadcrumbKey = segment === '' ? 'home' : segment;
      const breadcrumbName = t(`breadcrumbs.${breadcrumbKey}`, { defaultValue: segment });
      
      breadcrumbs.push({
        name: breadcrumbName,
        url: currentPath,
      });
    });

    return breadcrumbs;
  };

  /**
   * Generate canonical URL for current page
   */
  const getCanonicalUrl = (path?: string) => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const currentPath = path || location.pathname;
    return `${baseUrl}${currentPath}`;
  };

  /**
   * Generate alternate URLs for all languages
   */
  const getAlternateUrls = (path?: string) => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const currentPath = path || location.pathname;
    
    return {
      en: `${baseUrl}/en${currentPath === '/' ? '' : currentPath}`,
      nl: `${baseUrl}/nl${currentPath === '/' ? '' : currentPath}`,
      fr: `${baseUrl}/fr${currentPath === '/' ? '' : currentPath}`,
    };
  };

  return {
    // Page SEO data
    getPageSEO,
    
    // Structured data generators
    getWebsiteStructuredData,
    getArtistStructuredData,
    getBreadcrumbStructuredData,
    getFAQStructuredData,
    
    // URL utilities
    getCanonicalUrl,
    getAlternateUrls,
    getCurrentBreadcrumbs,
    
    // Current language
    currentLanguage,
  };
};
