import { useTranslation } from 'react-i18next';
import { useCallback } from 'react';
import { languages, supportedLanguages } from '@/lib/i18n';

/**
 * Custom hook for language management
 * Provides utilities for language switching and information
 */
export const useLanguage = () => {
  const { i18n } = useTranslation();

  // Get current language
  const currentLanguage = i18n.language;

  // Get current language info
  const getCurrentLanguageInfo = useCallback(() => {
    return languages.find(lang => lang.code === currentLanguage) || languages[0];
  }, [currentLanguage]);

  // Change language
  const changeLanguage = useCallback(async (languageCode: string) => {
    if (supportedLanguages.includes(languageCode)) {
      try {
        await i18n.changeLanguage(languageCode);
        
        // Store in both localStorage and sessionStorage for persistence
        localStorage.setItem('i18nextLng', languageCode);
        sessionStorage.setItem('i18nextLng', languageCode);
        
        // Optional: Update document language attribute
        document.documentElement.lang = languageCode;
        
        return true;
      } catch (error) {
        console.error('Failed to change language:', error);
        return false;
      }
    } else {
      console.warn(`Language ${languageCode} is not supported`);
      return false;
    }
  }, [i18n]);

  // Check if language is supported
  const isLanguageSupported = useCallback((languageCode: string) => {
    return supportedLanguages.includes(languageCode);
  }, []);

  // Get language direction (for RTL support in the future)
  const getLanguageDirection = useCallback((languageCode?: string) => {
    const lang = languageCode || currentLanguage;
    // Add RTL languages here if needed in the future
    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
    return rtlLanguages.includes(lang) ? 'rtl' : 'ltr';
  }, [currentLanguage]);

  // Get browser language
  const getBrowserLanguage = useCallback(() => {
    const browserLang = navigator.language.split('-')[0];
    return isLanguageSupported(browserLang) ? browserLang : 'en';
  }, [isLanguageSupported]);

  return {
    // Current language state
    currentLanguage,
    currentLanguageInfo: getCurrentLanguageInfo(),
    
    // Available languages
    languages,
    supportedLanguages,
    
    // Language operations
    changeLanguage,
    isLanguageSupported,
    getLanguageDirection,
    getBrowserLanguage,
    
    // i18n instance for advanced usage
    i18n,
  };
};
