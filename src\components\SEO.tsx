import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/hooks/useLanguage';
import { useLocation } from 'react-router-dom';

interface SEOProps {
  /**
   * Page-specific title (will be combined with site name)
   */
  title?: string;
  
  /**
   * Page description for meta tags
   */
  description?: string;
  
  /**
   * Keywords for the page
   */
  keywords?: string;
  
  /**
   * Canonical URL (optional, will be auto-generated if not provided)
   */
  canonicalUrl?: string;
  
  /**
   * Open Graph image URL
   */
  ogImage?: string;
  
  /**
   * Page type (website, article, etc.)
   */
  ogType?: string;
  
  /**
   * Whether this page should be indexed by search engines
   */
  noIndex?: boolean;
  
  /**
   * Whether search engines should follow links on this page
   */
  noFollow?: boolean;
  
  /**
   * Additional structured data (JSON-LD)
   */
  structuredData?: object;
  
  /**
   * Custom meta tags
   */
  customMeta?: Array<{
    name?: string;
    property?: string;
    content: string;
  }>;
}

/**
 * SEO Component for managing page meta tags, structured data, and multilingual SEO
 */
export const SEO: React.FC<SEOProps> = ({
  title,
  description,
  keywords,
  canonicalUrl,
  ogImage,
  ogType = 'website',
  noIndex = false,
  noFollow = false,
  structuredData,
  customMeta = [],
}) => {
  const { t } = useTranslation('seo');
  const { currentLanguage, languages } = useLanguage();
  const location = useLocation();
  
  // Get site information from translations
  const siteName = t('site.name');
  const siteDescription = t('site.description');
  const siteKeywords = t('site.keywords');
  const siteAuthor = t('site.author');
  const twitterHandle = t('site.twitterHandle');
  const defaultOgImage = t('site.ogImage');
  
  // Build full title
  const fullTitle = title ? `${title} | ${siteName}` : siteName;
  
  // Use provided values or fall back to site defaults
  const metaDescription = description || siteDescription;
  const metaKeywords = keywords || siteKeywords;
  const metaOgImage = ogImage || defaultOgImage;
  
  // Build canonical URL
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  const currentPath = location.pathname;
  const fullCanonicalUrl = canonicalUrl || `${baseUrl}${currentPath}`;
  
  // Generate alternate language URLs for hreflang
  const alternateUrls = languages.map(lang => ({
    lang: lang.code,
    url: `${baseUrl}/${lang.code}${currentPath === '/' ? '' : currentPath}`,
  }));
  
  // Robots meta content
  const robotsContent = [
    noIndex ? 'noindex' : 'index',
    noFollow ? 'nofollow' : 'follow',
  ].join(', ');

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <html lang={currentLanguage} />
      <title>{fullTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={metaKeywords} />
      <meta name="author" content={siteAuthor} />
      <meta name="robots" content={robotsContent} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullCanonicalUrl} />
      
      {/* Alternate Language Links (hreflang) */}
      {alternateUrls.map(({ lang, url }) => (
        <link
          key={lang}
          rel="alternate"
          hrefLang={lang}
          href={url}
        />
      ))}
      
      {/* x-default for international targeting */}
      <link
        rel="alternate"
        hrefLang="x-default"
        href={`${baseUrl}${currentPath}`}
      />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={fullCanonicalUrl} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={currentLanguage} />
      {metaOgImage && <meta property="og:image" content={metaOgImage} />}
      
      {/* Alternate locales for Open Graph */}
      {languages
        .filter(lang => lang.code !== currentLanguage)
        .map(lang => (
          <meta
            key={lang.code}
            property="og:locale:alternate"
            content={lang.code}
          />
        ))}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={twitterHandle} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={metaDescription} />
      {metaOgImage && <meta name="twitter:image" content={metaOgImage} />}
      
      {/* Additional Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Language" content={currentLanguage} />
      
      {/* Custom Meta Tags */}
      {customMeta.map((meta, index) => (
        <meta
          key={index}
          {...(meta.name ? { name: meta.name } : {})}
          {...(meta.property ? { property: meta.property } : {})}
          content={meta.content}
        />
      ))}
      
      {/* Structured Data (JSON-LD) */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEO;
