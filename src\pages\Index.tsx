
import React from 'react';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import ArtistSearch from '@/components/ArtistSearch';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { PageTransition } from '@/components/animations';
import SEO from '@/components/SEO';
import { useSEO } from '@/hooks/useSEO';

const Index = () => {
  const { getPageSEO, getWebsiteStructuredData } = useSEO();
  const homeSEO = getPageSEO('home');

  return (
    <>
      <SEO
        title={homeSEO.title}
        description={homeSEO.description}
        keywords={homeSEO.keywords}
        structuredData={getWebsiteStructuredData()}
      />
      <PageTransition className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
        {/* Cosmic particle effect (background dots) */}
        <div className="absolute inset-0 cosmic-grid opacity-30"></div>

        {/* Gradient glow effect */}
        <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
          <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
        </div>

        <div className="relative z-10 flex flex-col min-h-screen">
          <Header />
          <main>
            <HeroSection />
            <ArtistSearch />
          </main>
          <CTAStrip />
          <Footer />
        </div>
      </PageTransition>
    </>
  );
};

export default Index;
