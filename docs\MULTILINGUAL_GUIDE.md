# Multilingual Infrastructure Guide

This guide explains how to use the multilingual system implemented in your website.

## Overview

Your website now supports multiple languages using **react-i18next**, the industry standard for React internationalization. The system is designed to be:

- **Scalable**: Easy to add new languages and translations
- **Developer-friendly**: Simple hooks and components for translations
- **Performance-optimized**: Lazy loading of translation files
- **SEO-ready**: Proper language attributes and structure

## Supported Languages

- **English (EN)** - Default language
- **Dutch (NL)** - Nederlands
- **French (FR)** - Français

## File Structure

```
public/locales/
├── en/
│   ├── common.json      # Common translations (navigation, buttons, etc.)
│   └── pages.json       # Page-specific translations
├── nl/
│   ├── common.json
│   └── pages.json
└── fr/
    ├── common.json
    └── pages.json

src/
├── lib/i18n.ts          # i18n configuration
├── hooks/useLanguage.ts # Language management hook
└── components/
    ├── LanguageProvider.tsx # Language provider wrapper
    └── Translation.tsx      # Translation components
```

## How to Use Translations

### 1. Basic Translation Hook

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation('common'); // or 'pages'
  
  return (
    <div>
      <h1>{t('navigation.artists')}</h1>
      <p>{t('common.loading')}</p>
    </div>
  );
};
```

### 2. Language Management Hook

```tsx
import { useLanguage } from '@/hooks/useLanguage';

const MyComponent = () => {
  const { 
    currentLanguage, 
    changeLanguage, 
    languages 
  } = useLanguage();
  
  return (
    <div>
      <p>Current: {currentLanguage}</p>
      <button onClick={() => changeLanguage('nl')}>
        Switch to Dutch
      </button>
    </div>
  );
};
```

### 3. Translation Components

```tsx
import { T, Translation } from '@/components/Translation';

const MyComponent = () => {
  return (
    <div>
      {/* Simple text translation */}
      <T k="navigation.artists" />
      
      {/* Complex translation with HTML */}
      <Translation 
        i18nKey="welcome.message"
        values={{ name: 'John' }}
        components={{
          strong: <strong className="font-bold" />
        }}
      />
    </div>
  );
};
```

## Adding New Translations

### 1. Add to Translation Files

Add your new keys to the appropriate JSON files:

```json
// public/locales/en/common.json
{
  "myNewSection": {
    "title": "My New Title",
    "description": "My description"
  }
}
```

### 2. Use in Components

```tsx
const { t } = useTranslation('common');
return <h1>{t('myNewSection.title')}</h1>;
```

## Adding New Languages

### 1. Update Language Configuration

```typescript
// src/lib/i18n.ts
export const languages = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'nl', name: 'Dutch', nativeName: 'Nederlands' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'de', name: 'German', nativeName: 'Deutsch' }, // New language
];
```

### 2. Create Translation Files

Create new folders and files:
```
public/locales/de/
├── common.json
└── pages.json
```

### 3. Update Header Component

The language switcher will automatically pick up new languages from the configuration.

## Best Practices

### 1. Translation Key Naming

Use descriptive, hierarchical keys:
```json
{
  "pages": {
    "home": {
      "hero": {
        "title": "Welcome",
        "subtitle": "Discover amazing artists"
      }
    }
  }
}
```

### 2. Namespace Organization

- **common.json**: Navigation, buttons, common UI elements
- **pages.json**: Page-specific content
- **forms.json**: Form labels and validation messages (if needed)
- **errors.json**: Error messages (if needed)

### 3. Interpolation

Use interpolation for dynamic content:
```json
{
  "welcome": "Welcome, {{name}}!"
}
```

```tsx
t('welcome', { name: 'John' })
```

### 4. Pluralization

Handle plurals properly:
```json
{
  "items": "{{count}} item",
  "items_plural": "{{count}} items"
}
```

```tsx
t('items', { count: 5 })
```

## Language Persistence

The system automatically:
- Detects browser language on first visit
- Saves language choice in localStorage and sessionStorage
- Maintains language across page navigation
- Sets proper HTML lang attribute

## SEO Considerations

The system automatically:
- Sets `document.documentElement.lang` attribute
- Supports future implementation of language-specific URLs
- Provides proper language metadata

## Development Tips

### 1. Missing Translation Detection

In development mode, missing translations are logged to console.

### 2. Translation Loading

Translations are loaded asynchronously. The `LanguageProvider` handles loading states.

### 3. Testing Different Languages

Use browser dev tools or the language switcher to test different languages during development.

## Future Enhancements

The infrastructure supports:
- RTL language support (Arabic, Hebrew, etc.)
- Language-specific URLs (/en/about, /nl/over-ons)
- Dynamic translation loading
- Translation management systems integration
- Automatic translation services integration

## Troubleshooting

### Translation Not Showing

1. Check if the key exists in the JSON file
2. Verify the namespace is correct
3. Check browser console for missing key warnings
4. Ensure the component is wrapped in LanguageProvider

### Language Not Switching

1. Check if language code is in supported languages list
2. Verify translation files exist for that language
3. Check browser console for loading errors

## Performance

The system is optimized for performance:
- Translation files are cached by the browser
- Only used namespaces are loaded
- Language switching is instant after initial load
- Minimal bundle size impact
